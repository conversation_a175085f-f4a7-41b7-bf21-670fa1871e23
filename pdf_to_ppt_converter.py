#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转PPT和文本提取工具
将PDF文件转换为可编辑的PPT文件，并提取其中的文字保存为文本文件
"""

import os
import sys
from pathlib import Path
import fitz  # PyMuPDF
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
import io
from PIL import Image

def extract_text_from_pdf(pdf_path):
    """
    从PDF文件中提取文字
    
    Args:
        pdf_path (str): PDF文件路径
        
    Returns:
        str: 提取的文字内容
    """
    try:
        doc = fitz.open(pdf_path)
        text_content = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            text_content += f"=== 第 {page_num + 1} 页 ===\n"
            text_content += text + "\n\n"
        
        doc.close()
        return text_content
    except Exception as e:
        print(f"提取文字时出错: {e}")
        return ""

def pdf_to_ppt(pdf_path, output_dir):
    """
    将PDF转换为PPT文件
    
    Args:
        pdf_path (str): PDF文件路径
        output_dir (str): 输出目录
        
    Returns:
        str: 生成的PPT文件路径
    """
    try:
        # 打开PDF文件
        doc = fitz.open(pdf_path)
        
        # 创建新的PPT演示文稿
        prs = Presentation()
        
        # 设置幻灯片尺寸 (16:9)
        prs.slide_width = Inches(13.33)
        prs.slide_height = Inches(7.5)
        
        print(f"开始转换PDF，共 {len(doc)} 页...")
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            
            # 添加新幻灯片
            slide_layout = prs.slide_layouts[6]  # 空白布局
            slide = prs.slides.add_slide(slide_layout)
            
            # 提取页面文字
            text = page.get_text()
            
            # 如果有文字内容，添加文本框
            if text.strip():
                # 添加标题文本框
                title_box = slide.shapes.add_textbox(
                    Inches(0.5), Inches(0.5), 
                    Inches(12.33), Inches(1)
                )
                title_frame = title_box.text_frame
                title_frame.text = f"第 {page_num + 1} 页"
                title_frame.paragraphs[0].font.size = Pt(24)
                title_frame.paragraphs[0].font.bold = True
                title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
                
                # 添加内容文本框
                content_box = slide.shapes.add_textbox(
                    Inches(0.5), Inches(1.8), 
                    Inches(12.33), Inches(5.2)
                )
                content_frame = content_box.text_frame
                content_frame.text = text
                content_frame.paragraphs[0].font.size = Pt(14)
                content_frame.word_wrap = True
            
            # 将PDF页面转换为图像并添加到幻灯片
            try:
                # 获取页面图像
                mat = fitz.Matrix(2.0, 2.0)  # 放大2倍以提高质量
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # 将图像数据转换为PIL Image
                img = Image.open(io.BytesIO(img_data))
                
                # 保存临时图像文件
                temp_img_path = os.path.join(output_dir, f"temp_page_{page_num + 1}.png")
                img.save(temp_img_path)
                
                # 添加图像到幻灯片（作为背景或参考）
                # 计算合适的图像尺寸
                img_width = min(Inches(6), prs.slide_width * 0.45)
                img_height = img_width * img.height / img.width
                
                # 将图像添加到幻灯片右侧
                slide.shapes.add_picture(
                    temp_img_path,
                    Inches(7), Inches(1),
                    img_width, img_height
                )
                
                # 删除临时图像文件
                os.remove(temp_img_path)
                
            except Exception as img_error:
                print(f"处理第 {page_num + 1} 页图像时出错: {img_error}")
            
            print(f"已处理第 {page_num + 1} 页")
        
        # 保存PPT文件
        pdf_name = Path(pdf_path).stem
        ppt_path = os.path.join(output_dir, f"{pdf_name}_converted.pptx")
        prs.save(ppt_path)
        
        doc.close()
        print(f"PPT文件已保存到: {ppt_path}")
        return ppt_path
        
    except Exception as e:
        print(f"转换PPT时出错: {e}")
        return None

def save_text_to_file(text_content, output_path):
    """
    将文字内容保存到文本文件
    
    Args:
        text_content (str): 文字内容
        output_path (str): 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"文本文件已保存到: {output_path}")
    except Exception as e:
        print(f"保存文本文件时出错: {e}")

def main():
    """主函数"""
    # PDF文件路径
    pdf_path = r"D:\WXWork\1688855913515868\Cache\File\2025-08\citymart城市集市-公司资料2503.pdf"
    
    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"错误: 找不到文件 {pdf_path}")
        return
    
    # 获取输出目录（PDF文件所在目录）
    output_dir = os.path.dirname(pdf_path)
    pdf_name = Path(pdf_path).stem
    
    print(f"开始处理文件: {pdf_path}")
    print(f"输出目录: {output_dir}")
    
    # 1. 提取文字内容
    print("\n=== 提取文字内容 ===")
    text_content = extract_text_from_pdf(pdf_path)
    
    if text_content:
        # 保存文字到文本文件
        text_output_path = os.path.join(output_dir, f"{pdf_name}_extracted_text.txt")
        save_text_to_file(text_content, text_output_path)
    else:
        print("未能提取到文字内容")
    
    # 2. 转换为PPT
    print("\n=== 转换为PPT ===")
    ppt_path = pdf_to_ppt(pdf_path, output_dir)
    
    if ppt_path:
        print(f"\n✅ 转换完成!")
        print(f"📄 PPT文件: {ppt_path}")
        print(f"📝 文本文件: {text_output_path}")
    else:
        print("\n❌ 转换失败")

def install_requirements():
    """安装所需的依赖包"""
    required_packages = [
        "PyMuPDF",  # fitz
        "python-pptx",
        "Pillow"
    ]
    
    print("检查并安装所需依赖包...")
    for package in required_packages:
        try:
            __import__(package.replace("-", "_").lower())
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            os.system(f"pip install {package}")

if __name__ == "__main__":
    print("PDF转PPT和文本提取工具")
    print("=" * 50)
    
    # 检查并安装依赖
    try:
        import fitz
        from pptx import Presentation
        from PIL import Image
        print("✅ 所有依赖已就绪")
    except ImportError:
        print("📦 正在安装缺失的依赖包...")
        install_requirements()
        print("请重新运行脚本")
        sys.exit(1)
    
    # 运行主程序
    main()
