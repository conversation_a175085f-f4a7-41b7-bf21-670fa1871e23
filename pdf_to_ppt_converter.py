#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能PDF转PPT转换工具
将PDF文件智能转换为可编辑的PPT文件，保持原有的内容结构和格式
"""

import os
import sys
import re
from pathlib import Path
import fitz  # PyMuPDF
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.dml.color import RGBColor
import io
from PIL import Image
import json

def analyze_text_structure(text_blocks):
    """
    分析文本结构，识别标题、正文、列表等

    Args:
        text_blocks (list): 文本块列表

    Returns:
        list: 结构化的文本元素
    """
    structured_elements = []

    for block in text_blocks:
        text = block.get('text', '').strip()
        if not text:
            continue

        bbox = block.get('bbox', [0, 0, 0, 0])
        font_size = block.get('size', 12)
        font_flags = block.get('flags', 0)

        # 判断文本类型
        element_type = 'body'

        # 标题判断（字体大小、粗体、位置等）
        if font_size > 16 or (font_flags & 2**4):  # 大字体或粗体
            element_type = 'title'
        elif font_size > 14:
            element_type = 'subtitle'

        # 列表判断
        if re.match(r'^\s*[•·▪▫◦‣⁃]\s+', text) or re.match(r'^\s*\d+[.)]\s+', text):
            element_type = 'bullet'

        # 居中文本判断（可能是标题）
        if bbox[0] > 100 and element_type == 'body':  # 左边距较大
            element_type = 'centered'

        structured_elements.append({
            'type': element_type,
            'text': text,
            'bbox': bbox,
            'font_size': font_size,
            'font_flags': font_flags
        })

    return structured_elements

def extract_structured_content(pdf_path):
    """
    从PDF中提取结构化内容

    Args:
        pdf_path (str): PDF文件路径

    Returns:
        tuple: (页面内容列表, 全文文本)
    """
    try:
        doc = fitz.open(pdf_path)
        pages_content = []
        all_text = ""

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)

            # 获取文本块（包含格式信息）
            text_dict = page.get_text("dict")

            # 提取图片
            image_list = page.get_images()

            # 分析页面内容
            page_elements = []

            # 处理文本块
            for block in text_dict.get("blocks", []):
                if "lines" in block:  # 文本块
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_blocks = [{
                                'text': span.get('text', ''),
                                'bbox': span.get('bbox', [0, 0, 0, 0]),
                                'size': span.get('size', 12),
                                'flags': span.get('flags', 0),
                                'font': span.get('font', ''),
                                'color': span.get('color', 0)
                            }]

                            structured = analyze_text_structure(text_blocks)
                            page_elements.extend(structured)

            # 处理图片
            for img_index, img in enumerate(image_list):
                try:
                    # 获取图片信息
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    if pix.n - pix.alpha < 4:  # 确保不是CMYK
                        img_data = pix.tobytes("png")
                        page_elements.append({
                            'type': 'image',
                            'data': img_data,
                            'index': img_index,
                            'bbox': [0, 0, pix.width, pix.height]  # 实际位置需要从页面获取
                        })
                    pix = None
                except:
                    continue

            # 获取页面的纯文本用于全文保存
            page_text = page.get_text()
            all_text += f"=== 第 {page_num + 1} 页 ===\n{page_text}\n\n"

            pages_content.append({
                'page_num': page_num + 1,
                'elements': page_elements,
                'raw_text': page_text
            })

        doc.close()
        return pages_content, all_text

    except Exception as e:
        print(f"提取结构化内容时出错: {e}")
        return [], ""

def create_smart_ppt(pages_content, output_path):
    """
    智能创建PPT文件，保持原有结构和格式

    Args:
        pages_content (list): 页面内容列表
        output_path (str): 输出PPT文件路径

    Returns:
        str: 生成的PPT文件路径
    """
    try:
        # 创建新的PPT演示文稿
        prs = Presentation()

        # 设置幻灯片尺寸 (16:9)
        prs.slide_width = Inches(13.33)
        prs.slide_height = Inches(7.5)

        print(f"开始智能转换PDF，共 {len(pages_content)} 页...")

        for page_data in pages_content:
            page_num = page_data['page_num']
            elements = page_data['elements']

            # 添加新幻灯片
            slide_layout = prs.slide_layouts[6]  # 空白布局
            slide = prs.slides.add_slide(slide_layout)

            # 分组处理元素
            title_elements = [e for e in elements if e['type'] == 'title']
            subtitle_elements = [e for e in elements if e['type'] == 'subtitle']
            body_elements = [e for e in elements if e['type'] == 'body']
            bullet_elements = [e for e in elements if e['type'] == 'bullet']
            centered_elements = [e for e in elements if e['type'] == 'centered']
            image_elements = [e for e in elements if e['type'] == 'image']

            current_y = Inches(0.5)  # 起始Y位置

            # 添加标题
            if title_elements:
                title_text = '\n'.join([e['text'] for e in title_elements])
                title_box = slide.shapes.add_textbox(
                    Inches(0.5), current_y,
                    Inches(12.33), Inches(1.2)
                )
                title_frame = title_box.text_frame
                title_frame.text = title_text
                title_frame.paragraphs[0].font.size = Pt(28)
                title_frame.paragraphs[0].font.bold = True
                title_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
                current_y += Inches(1.5)

            # 添加副标题
            if subtitle_elements:
                subtitle_text = '\n'.join([e['text'] for e in subtitle_elements])
                subtitle_box = slide.shapes.add_textbox(
                    Inches(0.5), current_y,
                    Inches(12.33), Inches(0.8)
                )
                subtitle_frame = subtitle_box.text_frame
                subtitle_frame.text = subtitle_text
                subtitle_frame.paragraphs[0].font.size = Pt(20)
                subtitle_frame.paragraphs[0].font.bold = True
                subtitle_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
                current_y += Inches(1.0)

            # 添加居中文本
            if centered_elements:
                centered_text = '\n'.join([e['text'] for e in centered_elements])
                centered_box = slide.shapes.add_textbox(
                    Inches(1), current_y,
                    Inches(11.33), Inches(0.8)
                )
                centered_frame = centered_box.text_frame
                centered_frame.text = centered_text
                centered_frame.paragraphs[0].font.size = Pt(16)
                centered_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
                current_y += Inches(1.0)

            # 计算内容区域
            content_width = Inches(12.33)
            if image_elements:
                content_width = Inches(7.5)  # 为图片留出空间

            # 添加列表项
            if bullet_elements:
                bullet_text = '\n'.join([f"• {e['text'].lstrip('•·▪▫◦‣⁃').strip()}" for e in bullet_elements])
                bullet_box = slide.shapes.add_textbox(
                    Inches(0.5), current_y,
                    content_width, Inches(2.5)
                )
                bullet_frame = bullet_box.text_frame
                bullet_frame.text = bullet_text
                bullet_frame.paragraphs[0].font.size = Pt(14)
                bullet_frame.word_wrap = True
                current_y += Inches(2.8)

            # 添加正文内容
            if body_elements:
                # 过滤掉已经作为标题或列表处理的文本
                body_text = '\n\n'.join([e['text'] for e in body_elements
                                       if len(e['text']) > 10])  # 过滤短文本

                if body_text.strip():
                    remaining_height = Inches(7.5) - current_y - Inches(0.5)
                    if remaining_height > Inches(1):
                        body_box = slide.shapes.add_textbox(
                            Inches(0.5), current_y,
                            content_width, remaining_height
                        )
                        body_frame = body_box.text_frame
                        body_frame.text = body_text
                        body_frame.paragraphs[0].font.size = Pt(12)
                        body_frame.word_wrap = True

            # 添加图片
            if image_elements:
                img_y = Inches(1.5)
                for i, img_element in enumerate(image_elements[:2]):  # 最多2张图片
                    try:
                        # 保存临时图片文件
                        temp_img_path = f"temp_img_{page_num}_{i}.png"
                        with open(temp_img_path, 'wb') as f:
                            f.write(img_element['data'])

                        # 添加图片到幻灯片
                        slide.shapes.add_picture(
                            temp_img_path,
                            Inches(8.5), img_y,
                            Inches(4.5), Inches(2.5)
                        )

                        # 删除临时文件
                        os.remove(temp_img_path)
                        img_y += Inches(3)

                    except Exception as img_error:
                        print(f"处理第 {page_num} 页图片时出错: {img_error}")

            print(f"已处理第 {page_num} 页")

        # 保存PPT文件
        prs.save(output_path)
        print(f"智能PPT文件已保存到: {output_path}")
        return output_path

    except Exception as e:
        print(f"创建智能PPT时出错: {e}")
        return None

def save_text_to_file(text_content, output_path):
    """
    将文字内容保存到文本文件
    
    Args:
        text_content (str): 文字内容
        output_path (str): 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"文本文件已保存到: {output_path}")
    except Exception as e:
        print(f"保存文本文件时出错: {e}")

def main():
    """主函数"""
    # PDF文件路径
    pdf_path = r"D:\WXWork\1688855913515868\Cache\File\2025-08\citymart城市集市-公司资料2503.pdf"

    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"错误: 找不到文件 {pdf_path}")
        return

    # 获取输出目录（PDF文件所在目录）
    output_dir = os.path.dirname(pdf_path)
    pdf_name = Path(pdf_path).stem

    print(f"开始智能处理文件: {pdf_path}")
    print(f"输出目录: {output_dir}")

    # 1. 提取结构化内容
    print("\n=== 分析PDF结构和内容 ===")
    pages_content, all_text = extract_structured_content(pdf_path)

    if not pages_content:
        print("未能提取到内容")
        return

    # 2. 保存文字到文本文件
    print("\n=== 保存提取的文字 ===")
    text_output_path = os.path.join(output_dir, f"{pdf_name}_extracted_text.txt")
    save_text_to_file(all_text, text_output_path)

    # 3. 智能转换为PPT
    print("\n=== 智能转换为可编辑PPT ===")
    ppt_output_path = os.path.join(output_dir, f"{pdf_name}_smart_converted.pptx")
    ppt_path = create_smart_ppt(pages_content, ppt_output_path)

    if ppt_path:
        print(f"\n✅ 智能转换完成!")
        print(f"📄 可编辑PPT文件: {ppt_path}")
        print(f"📝 文本文件: {text_output_path}")
        print(f"\n🎯 转换特点:")
        print(f"   • 保持原文档的结构层次")
        print(f"   • 自动识别标题、正文、列表")
        print(f"   • 提取并重新布局图片")
        print(f"   • 生成完全可编辑的PPT内容")
    else:
        print("\n❌ 转换失败")

def install_requirements():
    """安装所需的依赖包"""
    required_packages = [
        "PyMuPDF",  # fitz
        "python-pptx",
        "Pillow"
    ]
    
    print("检查并安装所需依赖包...")
    for package in required_packages:
        try:
            __import__(package.replace("-", "_").lower())
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            os.system(f"pip install {package}")

if __name__ == "__main__":
    print("🚀 智能PDF转PPT转换工具")
    print("=" * 60)
    print("📋 功能特点:")
    print("   • 智能识别文档结构（标题、正文、列表）")
    print("   • 保持原有格式和布局")
    print("   • 提取图片并重新布局")
    print("   • 生成完全可编辑的PPT文件")
    print("=" * 60)

    # 检查并安装依赖
    try:
        import fitz
        from pptx import Presentation
        from PIL import Image
        print("✅ 所有依赖已就绪")
    except ImportError:
        print("📦 正在安装缺失的依赖包...")
        install_requirements()
        print("请重新运行脚本")
        sys.exit(1)

    # 运行主程序
    main()
